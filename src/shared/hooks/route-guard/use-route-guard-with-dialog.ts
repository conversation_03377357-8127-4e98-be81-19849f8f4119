'use client';

import { useCallback, useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { useRouteGuard } from './use-route-guard';

type UseRouteGuardWithDialogOptions = {
  when: boolean;
  title?: string;
  message?: string;
};

export function useRouteGuardWithDialog({
  when,
  title = 'Unsaved Changes',
  message = 'You have unsaved changes. Are you sure you want to leave?',
}: UseRouteGuardWithDialogOptions) {
  const [showDialog, setShowDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<'navigate' | 'refresh' | null>(null);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition(); // Next.js 15 useTransition
  const router = useRouter();

  const handleRouteChangeStart = useCallback(
    (href?: string) => {
      if (when) {
        if (href === 'refresh') {
          setPendingAction('refresh');
        } else {
          setPendingAction('navigate');
          setPendingNavigation(href || null);
        }
        setShowDialog(true);
        return false; // Block action initially
      }
      return true;
    },
    [when],
  );

  const handleConfirm = useCallback(() => {
    setShowDialog(false);

    if (pendingAction === 'refresh') {
      // Use startTransition for better UX in Next.js 15
      startTransition(() => {
        window.location.reload();
      });
    } else if (pendingAction === 'navigate' && pendingNavigation) {
      // Use startTransition for smoother navigation in Next.js 15
      startTransition(() => {
        router.push(pendingNavigation);
      });
    }

    setPendingAction(null);
    setPendingNavigation(null);
  }, [pendingAction, pendingNavigation, router, startTransition]);

  const handleCancel = useCallback(() => {
    setShowDialog(false);
    setPendingAction(null);
    setPendingNavigation(null);
  }, []);

  useRouteGuard({
    when,
    message,
    onRouteChangeStart: handleRouteChangeStart,
  });

  return {
    showDialog,
    title,
    message:
      pendingAction === 'refresh' ? 'You have unsaved changes. Are you sure you want to refresh the page?' : message,
    onConfirm: handleConfirm,
    onCancel: handleCancel,
    isPending, // Expose pending state for better UX
  };
}

'use client';

import dynamic from 'next/dynamic';
import { useCallback, useEffect, useRef, useState } from 'react';
import 'react-quill-new/dist/quill.snow.css';
import { htmlToMarkdown, markdownToHtml } from './parser';
import type { EditorContentChanged } from '@/shared/types/global';

const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });

const TOOLBAR_OPTIONS = [
  [{ header: [1, 2, 3, false] }],
  ['bold', 'italic', 'underline', 'strike', 'blockquote', 'link'],
  [{ list: 'ordered' }, { list: 'bullet' }],
  [{ indent: '-1' }, { indent: '+1' }],
  ['clean'],
];

export default function Editor({
  value: initialValue = '',
  onChange,
  debounceMs = 300,
}: {
  value?: string;
  onChange?: (value: EditorContentChanged) => void;
  debounceMs?: number;
}) {
  const [value, setValue] = useState<string>(() => markdownToHtml(initialValue || ''));
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedOnChange = useCallback((content: string) => {
    if (onChange) {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      debounceRef.current = setTimeout(() => {
        onChange({
          html: content,
          markdown: htmlToMarkdown(content),
        });
      }, debounceMs);
    }
  }, [onChange, debounceMs]);

  const handleChange = (content: string) => {
    setValue(content);
    debouncedOnChange(content);
  };

  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <ReactQuill
      theme="snow"
      placeholder="Start writing..."
      modules={{
        toolbar: {
          container: TOOLBAR_OPTIONS,
        },

      }}
      value={value}
      onChange={handleChange}
    />
  );
}

'use client';
import type { ApexOptions } from 'apexcharts';
import dynamic from 'next/dynamic';
import React, { memo } from 'react';
import { useThemeStyles } from '@/shared/hooks/useThemeStyles';
import { cn } from '@/shared/utils/utils';
import { horizontalBarChartLoadingVariants, horizontalBarChartVariants } from './horizontal-bar-chart-variants';

// Default colors - using CSS variables for theming
const defaultColors = ['var(--color-primary)'];

// Dynamically import the ReactApexChart component with loading placeholder
const ReactApexChart = dynamic(
  () => import('react-apexcharts').then(mod => mod.default),
  {
    ssr: false,
    loading: () => {
      const LoadingComponent = () => {
        return (
          <div className={horizontalBarChartLoadingVariants()}>
            <div className={cn(horizontalBarChartLoadingVariants({ variant: 'default' }), 'animate-pulse, bg-gray-200')}></div>
          </div>
        );
      };
      return <LoadingComponent />;
    },
  },
);

export type HorizontalBarChartProps = {
  title?: string;
  series: {
    name: string;
    data: number[];
  }[];
  categories: string[];
  colors?: string[];
  height?: number;
  maxValue?: number;
  showDataLabels?: boolean;
  className?: string;
  barWidth?: number; // New property to control bar width
};

const HorizontalBarChart = memo(({
  title,
  series,
  categories,
  colors = defaultColors,
  height = 240, // Increased height by 40px
  maxValue,
  showDataLabels = true,
  className = '',
  barWidth = 70, // Default bar width percentage
}: HorizontalBarChartProps) => {
  const { isThemeDark } = useThemeStyles();

  const options: ApexOptions = {
    chart: {
      fontFamily: 'Lexend Deca, sans-serif',
      type: 'bar',
      height,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        speed: 800,
      },
      parentHeightOffset: 0,
      foreColor: 'var(--color-muted-foreground)', // Use CSS variable for text color
    },
    colors,
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: '60%', // Maintain good bar height for visibility
        borderRadius: 4,
        distributed: series.length === 1, // Use distributed if only one series
        dataLabels: {
          position: 'bottom', // Position labels at the start of the bar
        },
      },
    },
    dataLabels: {
      enabled: showDataLabels,
      textAnchor: 'middle', // Align text to the start
      style: {
        colors: ['#fff'], // White text color
        fontWeight: 'bold',
        fontSize: '16px', // Larger font for better visibility
      },
      formatter(val) {
        return val.toString();
      },
      offsetX: 14, // Add a small offset to move the label inside the bar
    },
    xaxis: {
      categories,
      labels: {
        show: false, // Hide x-axis labels
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      max: maxValue,
      range: barWidth, // Control the width of the bars by setting the range
    },
    yaxis: {
      labels: {
        style: {
          colors: 'var(--color-muted-foreground)', // Use CSS variable for text color
          fontSize: '12px',
          cssClass: 'font-italic',
        },
        offsetX: -10,
        maxWidth: 150, // Set maximum width for labels
      },
      axisBorder: {
        show: false,
      },
    },
    grid: {
      show: false,
      padding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 10, // Add some padding on the left for labels
      },
    },
    legend: {
      show: series.length > 1,
      position: 'top',
      horizontalAlign: 'left',
      fontSize: '13px',
    },
    tooltip: {
      shared: true,
      intersect: false,
      theme: isThemeDark() ? 'dark' : 'light', // Use theme-aware tooltip
    },
  };

  if (title) {
    options.title = {
      text: title,
      align: 'left',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: 'var(--color-foreground)', // Use CSS variable for title color
      },
    };
  }

  return (
    <div className={cn(horizontalBarChartVariants(), className)}>
      <ReactApexChart
        options={options}
        series={series}
        type="bar"
        height={height}
      />
    </div>
  );
});

export default HorizontalBarChart;

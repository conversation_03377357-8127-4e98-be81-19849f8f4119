export enum ENameStateAgentCopilotkit {
  SUMMARIZE = 'client_summarize_state',
  ASSESSMENT = 'client_assessment_state',
  ANALYSIS = 'brief_analysis_state',
  EDITING = 'content_editing_state',
  SCOPE = 'sow_analysis_state',
  QUOTATION = 'quotation_analysis_state',
};

export enum EEndpointApiCopilotkit {
  SUMMARIZE = 'client-summarize',
  ASSESSMENT = 'client-assessment',
  ANALYSIS = 'brief-analysis',
  EDITING = 'content_editing_state',
  SCOPE = 'sow-analysis',
  QUOTATION = 'quotation-analysis',
  ANSWER = 'answer-question-analysis',
}

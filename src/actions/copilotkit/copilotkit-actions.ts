'use server';

import { Env } from '@/core/config/Env';
import { getUserFromSession } from '@/features/auth/session';
import type { ServerActionResponse } from '@/shared/types/global';
import { cookies } from 'next/headers';

/**
 * Server action to make authenticated POST requests to copilotkit APIs
 * This action calls the copilotkit-api route which handles token validation and copilotkit API communication
 *
 * @param data The parameters for the copilotkit API request
 * @param endpoint The parameters for the copilotkit API request
 * @returns Promise that resolves to the copilotkit API response with a standardized response format
 */
export async function copilotkitAction<T>(
  data: T,
  endpoint: string,
): Promise<ServerActionResponse<any>> {
  try {
    // Make request to our internal API route which handles authentication
    const cookieStore = await cookies();
    const user = await getUserFromSession(cookieStore);
    if (!user) {
      return {
        success: false,
        error: 'Authentication token not found.',

      };
    }
    const response = await fetch(`${Env.NEXT_PUBLIC_RUNTIME_API_URL}/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    const responseData = await response.json();
    if (!response.ok) {
      return {
        success: false,
        error: responseData.error || 'Failed to make copilotkit API request',
      };
    }

    // Return successful response with data
    return {
      success: true,
      data: {
        statusCode: responseData.statusCode,
        message: responseData.message,
        data: responseData.result,
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('Error in copilotkitAPI action:', error);
    return { success: false, error: errorMessage };
  }
}

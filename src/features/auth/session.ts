import type { Cookies } from './types';
import { http } from '../../core/http/http';
import { COOKIE_SESSION_KEY } from './constant';

export function getUserFromSession(cookies: Pick<Cookies, 'get'>) {
  const sessionId = cookies.get(COOKIE_SESSION_KEY)?.value;

  if (sessionId == null) {
    return null;
  }

  return getUserSessionById(sessionId);
}

// function setCookie(sessionId: string, cookies: Pick<Cookies, 'set'>) {
//   cookies.set(COOKIE_SESSION_KEY, sessionId, {
//     secure: true,
//     httpOnly: true,
//     sameSite: 'lax',
//     expires: Date.now() + SESSION_EXPIRATION_SECONDS * 1000,
//   });
// }

async function getUserSessionById(sessionId: string): Promise<any> {
  try {
    const response = await http.get({
      url: '/auth/profile',
      options: {
        headers: {
          Authorization: `Bearer ${sessionId}`,
        },
      },
    });

    return response.data || null;
  } catch (error) {
    console.error('Failed to get user session:', error);
    return null;
  }
}

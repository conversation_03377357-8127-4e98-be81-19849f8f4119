import type { LoginCredentials, LoginResponse, User } from '../types/auth.types';
import type { ApiResponse } from '@/shared/types/api-response';
import { http } from '@/core/http/http';
import { signInSchema } from '@/features/auth/services/auth.validation';

/**
 * Login API call
 *
 * @param credentials - Login credentials
 * @returns Promise with login response or error
 */
export const loginApi = async (
  credentials: LoginCredentials,
): Promise<ApiResponse<LoginResponse>> => http.post<LoginResponse>({
  url: '/auth/login',
  data: credentials,
  schemaValidation: signInSchema,
});

/**
 * Logout API call
 *
 * @returns Promise with logout response or error
 */
export const logoutApi = async (): Promise<ApiResponse<any>> => http.post<any>({
  url: '/auth/logout',
});

/**
 * Get current user profile API call
 *
 * @returns Promise with user profile or error
 */
export const getCurrentUserApi = async (): Promise<ApiResponse<User>> => http.get<User>({
  url: '/auth/profile',
});

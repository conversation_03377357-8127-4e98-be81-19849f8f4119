import type { TokenOptions } from '../types/auth.types';
import Cookies from 'js-cookie';
import { COOKIE_SESSION_KEY, SESSION_EXPIRATION_SECONDS } from '../constant';

/**
 * Default token options
 */
const DEFAULT_TOKEN_OPTIONS: TokenOptions = {
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax',
  expires: SESSION_EXPIRATION_SECONDS,
};

/**
 * Set the access token in cookies
 *
 * @param token - The access token to store
 * @param options - Cookie options
 */
export const setToken = (
  token: string,
  options: TokenOptions = DEFAULT_TOKEN_OPTIONS,
  tokenKey: string = COOKIE_SESSION_KEY,
): void => {
  if (!token) {
    return;
  }

  // Calculate expiration date if expires is provided in seconds
  const expires = options.expires
    ? new Date(Date.now() + options.expires * 1000)
    : undefined;

  // Set the cookie
  Cookies.set(tokenKey, token, {
    secure: options.secure,
    sameSite: options.sameSite,
    expires,
  });
};

/**
 * Get the access token from cookies
 *
 * @param tokenKey - The cookie name for the access token
 * @returns The access token or null if not found
 */
export const getToken = (tokenKey: string = COOKIE_SESSION_KEY): string | null => {
  return Cookies.get(tokenKey) || null;
};

/**
 * Remove the access token from cookies
 *
 * @param tokenKey - The cookie name for the access token
 */
export const removeToken = (tokenKey: string = COOKIE_SESSION_KEY): void => {
  Cookies.remove(tokenKey);
};

/**
 * Check if the user is authenticated based on the presence of a token
 *
 * @param tokenKey - The cookie name for the access token
 * @returns True if authenticated, false otherwise
 */
export const isAuthenticated = (tokenKey: string = COOKIE_SESSION_KEY): boolean => {
  return !!getToken(tokenKey);
};

/**
 * Parse JWT token to get payload
 * Note: This is not a secure way to validate tokens, only for extracting info
 *
 * @param token - The JWT token
 * @returns The decoded payload or null if invalid
 */
export const parseToken = (token: string): any | null => {
  try {
    // Split the token and get the payload part (second part)
    const base64Url = token.split('.')[1];
    if (!base64Url) {
      return null;
    }

    // Convert base64url to base64
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

    // Decode and parse as JSON
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => `%${(`00${c.charCodeAt(0).toString(16)}`).slice(-2)}`)
        .join(''),
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error parsing token:', error);
    return null;
  }
};

/**
 * Check if token is expired
 *
 * @param token - The JWT token
 * @returns True if expired, false otherwise
 */
export const isTokenExpired = (token: string): boolean => {
  const payload = parseToken(token);
  if (!payload || !payload.exp) {
    return true;
  }

  // exp is in seconds, Date.now() is in milliseconds
  return payload.exp * 1000 < Date.now();
};

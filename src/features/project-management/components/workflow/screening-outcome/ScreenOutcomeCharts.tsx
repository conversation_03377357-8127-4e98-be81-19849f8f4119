'use client';

import type { OverallScore } from '../../../types/evaluation-form';
import type { ScreeningCriteria, ScreeningOutcomeData } from '../../../types/screening-outcome';
import { SectionType } from '../../../types/evaluation-form';
import React, { useEffect, useMemo } from 'react';
import HorizontalBarChart from '@/shared/components/charts/bar/HorizontalBarChart';
import { DonutChart } from '@/shared/components/charts/pie/DonutChart';
import { useEvaluationActions, useOverallScore } from '../../../stores/project-workflow-store';
import { mapScreeningOutcomeToChartData } from '../../../utils/screeningOutcomeMapper';

// Adapter function to convert OverallScore to ScreeningOutcomeData
const adaptOverallScoreToScreeningData = (score: OverallScore): ScreeningOutcomeData => {
  // Convert each section's data to ensure all required fields are present
  const convertSection = (section: any[]): ScreeningCriteria[] => {
    return section.map(item => ({
      ...item,
      // Ensure required fields have default values if they're undefined
      confidence: item.confidence || '0',
      citation: item.citation || '',
    }));
  };

  return {
    score: score.score,
    percentile: score.percentile,
    rank: score.rank,
    data: {
      clientProfileSection: convertSection(score.data.clientProfileSection),
      financialCapacitySection: convertSection(score.data.financialCapacitySection),
      collaborationSection: convertSection(score.data.collaborationSection),
      growthPotentialSection: convertSection(score.data.growthPotentialSection),
    },
  };
};

export default function ScreenOutcomeCharts() {
  // Get data from the evaluation store
  const overallScore = useOverallScore();
  const { getFinalOverallScore } = useEvaluationActions();

  // Calculate the final score when the component mounts and whenever it's shown
  useEffect(() => {
    getFinalOverallScore();
  }, [getFinalOverallScore]);

  // Force a recalculation every second to ensure we have the latest data
  useEffect(() => {
    const intervalId = setInterval(() => {
      getFinalOverallScore();
    }, 1000);

    return () => clearInterval(intervalId);
  }, [getFinalOverallScore]);

  // Memoize the chart data to prevent unnecessary re-renders
  const chartData = useMemo(() => {
    // If store has data, map it to chart format
    if (overallScore && overallScore.data
      && overallScore.data[SectionType.CLIENT_PROFILE].length > 0) {
      try {
        // Use the adapter to convert the data to the expected format
        const adaptedData = adaptOverallScoreToScreeningData(overallScore);
        return mapScreeningOutcomeToChartData(adaptedData);
      } catch (error) {
        console.error('Error mapping screening outcome data:', error);
      }
    }

    // Fallback data if no data is provided or mapping fails
    // Criteria Proportion Data
    const criteriaData = [
      { name: 'Extremely important', amount: 15 },
      { name: 'Important', amount: 23 },
      { name: 'Standard', amount: 62 },
    ];

    // Section Proportion Data
    const sectionData = [
      { name: 'Section A', amount: 23 },
      { name: 'Section B', amount: 23 },
      { name: 'Section C', amount: 23 },
      { name: 'Section D', amount: 23 },
    ];

    // Section A - Customer Profile Data
    const sectionACategories = [
      'Business Type',
      'Industry & ICP Fit',
      'Revenue Size',
      'Annual Marketing Budget',
      'Contract Type',
    ];
    const sectionASeries = [
      {
        name: 'Score',
        data: [5, 3, 4, 1, 2],
      },
    ];

    // Section B - Financial Capacity & Collaboration history Data
    const sectionBCategories = [
      'Payment History',
      'History of working with MVV Group',
      'Number of Project/Year',
      'Access to Decision Makers',
      'Decision Making Process',
    ];
    const sectionBSeries = [
      {
        name: 'Score',
        data: [5, 3, 4, 1, 5],
      },
    ];

    // Section C - Collaboration & Working Process Data
    const sectionCCategories = [
      'Decision-Making Process Complexity',
      'Marketing Maturity',
      'Information Sharing Readiness',
      'Project Collaboration Initiative',
      'Working Culture Fit',
    ];
    const sectionCSeries = [
      {
        name: 'Score',
        data: [5, 4, 2, 1, 5],
      },
    ];

    // Section D - Development Potential & Long-term Collaboration
    const sectionDCategories = [
      'Vision & Long-Term Commitment',
      'Multi-service Cooperation Potential',
      'Cross-Selling Potential',
      'Industry Influence',
      'Reference & Business Opportunity Potential',
    ];
    const sectionDSeries = [
      {
        name: 'Score',
        data: [5, 4, 3, 1, 5],
      },
    ];

    return {
      criteriaData,
      sectionData,
      sectionACategories,
      sectionASeries,
      sectionBCategories,
      sectionBSeries,
      sectionCCategories,
      sectionCSeries,
      sectionDCategories,
      sectionDSeries,
    };
  }, [overallScore]);

  return (
    <div className="space-y-8">
      <div className="rounded-2xl border border-border bg-background p-5 sm:p-6">

        {/* First row - Pie Charts side by side */}
        <div className="mb-12 grid grid-cols-1 gap-10 md:grid-cols-2">
          <div className="flex justify-center">
            {/* Criteria Proportion Pie Chart */}
            <div style={{ width: '400px', overflow: 'visible' }}>
              <DonutChart
                label="Criteria Proportion"
                data={chartData.criteriaData}
              />
            </div>
          </div>

          <div className="flex justify-center">
            {/* Section Proportion Pie Chart */}
            <div style={{ width: '400px', overflow: 'visible' }}>
              <DonutChart
                label="Section Proportion"
                data={chartData.sectionData}
              />
            </div>
          </div>
        </div>

        {/* Second row - Bar Charts in a 2x2 grid */}
        <div className="grid grid-cols-1 gap-x-10 gap-y-10 md:grid-cols-2">
          {/* Section A - Customer Profile */}
          <div>
            <p className="mb-4 font-medium">
              Section A - Customer Profile
            </p>
            <div className="pl-4">
              <HorizontalBarChart
                series={chartData.sectionASeries}
                categories={chartData.sectionACategories}
                colors={['#0079FF']}
                height={240}
                maxValue={5}
                showDataLabels={true}
                barWidth={60}
              />
            </div>
          </div>

          {/* Section C - Collaboration & Working Process */}
          <div>
            <p className="mb-4 font-medium">
              Section C - Collaboration & Working Process
            </p>
            <div className="pl-4">
              <HorizontalBarChart
                series={chartData.sectionCSeries}
                categories={chartData.sectionCCategories}
                colors={['#22D3EE']}
                height={240}
                maxValue={5}
                showDataLabels={true}
                barWidth={60}
              />
            </div>
          </div>

          {/* Section B - Financial Capacity & Collaboration history */}
          <div>
            <p className="mb-4 font-medium">
              Section B - Financial Capacity & Collaboration history
            </p>
            <div className="pl-4">
              <HorizontalBarChart
                series={chartData.sectionBSeries}
                categories={chartData.sectionBCategories}
                colors={['#FB923C']}
                height={240}
                maxValue={5}
                showDataLabels={true}
                barWidth={60}
              />
            </div>
          </div>

          {/* Section D - Development Potential & Long-term Collaboration */}
          <div>
            <p className="mb-4 font-medium">
              Section D - Development Potential & Long-term Collaboration
            </p>
            <div className="pl-4">
              <HorizontalBarChart
                series={chartData.sectionDSeries}
                categories={chartData.sectionDCategories}
                colors={['#805CDB']}
                height={240}
                maxValue={5}
                showDataLabels={true}
                barWidth={60}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

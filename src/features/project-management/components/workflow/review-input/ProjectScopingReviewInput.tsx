'use client';

import React, { useEffect, useState } from 'react';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useCurrentStep, useCurrentTask, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useCoAgent } from '@copilotkit/react-core';
import type { stateRouteAgent } from '@/shared/types/global';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import type { scopeOfWorkFlow } from '@/features/project-management/types/agent';

const ProjectScopingReviewInput: React.FC = () => {
  const [markdown, setMarkdown] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const currentStep = useCurrentStep();
  const currentStepId = currentStep?.id;

  const currentTask = useCurrentTask();

  const { completeStep } = useWorkflowActions();

  const { mutateAsync } = useUpdateStatusStep();

  const { data } = useGetInfoDetail<any, any>(currentStepId ?? '');

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const updateMarkdownToState = (data: string) => {
    const updateData = () => {
      setMarkdown(data);
      setIsLoading(false);
    };

    updateData();
    return true;
  };

  // AI agent hooks
  // const { appendMessage } = useCopilotChat();
  const { setState: _setCoAgentsState } = useCoAgent<stateRouteAgent<scopeOfWorkFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
    },
  });

  // const handleSendPrompt = React.useCallback((markdown: string) => {
  //   setCoAgentsState((prevState: any) => ({
  //     ...prevState,
  //     agent_name: AGENT_NAME_COPILOTKIT.SCOPE,

  //     [ENameStateAgentCopilotkit.SCOPE]: {
  //       ...prevState[ENameStateAgentCopilotkit.SCOPE],
  //       brief_analysis: markdown,
  //     },
  //   }));

  //   appendMessage(
  //     new TextMessage({
  //       content: MESSAGE_SEND_ROUTE_AGENT,
  //       role: Role.Developer,
  //     }),
  //   );
  // }, [setCoAgentsState, appendMessage]);

  useEffect(() => {
    if (data?.stepInfoPrevious.length && data?.stepInfoPrevious[0]?.infos?.length && data?.stepInfoPrevious[0]?.infos[0]?.value) {
      updateMarkdownToState(data?.stepInfoPrevious[0]?.infos[0]?.value);
    }
  }, [data]);

  const handleSubmit = async () => {
    if (!currentStepId) {
      return;
    }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
        },
      ],
    };

    await updateQuestionAnswer(payload, currentStepId);
    // handleSendPrompt(markdown);
    if (currentStep.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });

      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }

    completeStep(currentStepId);
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">Loading</div>
          <ProjectCardSkeleton />

        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6">

            <WorkflowNavigation
              onComplete={handleSubmit}
              nextButtonText="Start Generate"
              showPrevious={true}
              prevButtonText="Back"
              panelClass=""
            />
          </div>

          <div className="">

            <MarkdownRenderer content={markdown} />
          </div>
        </div>
      );
};

export default ProjectScopingReviewInput;

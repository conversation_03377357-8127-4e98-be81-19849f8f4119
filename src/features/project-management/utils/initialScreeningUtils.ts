import { z } from 'zod';
import type { IFileResponse } from '@/shared/types/global';
import type { StepInfosQA } from '../types/step';
import { Env } from '@/core/config/Env';

// Dynamic form data type
export type DynamicFormData = Record<string, string>;

/**
 * Generate dynamic Zod schema based on QA list
 *
 * @param qaList - Array of QA items
 * @returns Zod schema object
 */
export function createDynamicSchema(qaList: StepInfosQA[]): z.ZodObject<any> {
  const schemaFields: Record<string, z.ZodString> = {};

  qaList.forEach((qa) => {
    schemaFields[qa.name] = z.string();
  });

  return z.object(schemaFields).catchall(z.string().optional());
}

/**
 * Generate default form values from current step data
 *
 * @param qaList - Array of QA items
 * @param currentStepData - Current step data
 * @returns Default form values
 */
export function generateDefaultValues(
  qaList: StepInfosQA[],
  currentStepData?: any,
): DynamicFormData {
  const defaultValues: DynamicFormData = {};

  qaList.forEach((qa) => {
    defaultValues[qa.name] = currentStepData?.[qa.name] || '';
  });

  return defaultValues;
}

/**
 * Create prompt data for AI processing
 *
 * @param formData - Form data
 * @param files - Uploaded files
 * @returns Formatted prompt data
 */
export function createPromptData(
  formData: DynamicFormData,
  files: IFileResponse[],
) {
  const questions = Object.keys(formData).map(key => ({
    questions: key,
    answer: formData[key],
  }));

  return {
    questions,
    files: getFile(files),
  };
}

export function getFile(files: IFileResponse[]) {
  return files.map(file => ({
    id: file._id ?? file.id,
    url: file.url,
    originalname: file.originalname,
    filename: file.filename,
    key: `${Env.NEXT_PUBLIC_API_SERVER}/public/${file.key ?? file.filename}`,
  }));
}

/**
 * Check if data has changed to prevent unnecessary updates
 *
 * @param currentData - Current data
 * @param newData - New data to compare
 * @returns True if data has changed
 */
export function hasDataChanged(currentData: any, newData: any): boolean {
  if (!currentData && newData) {
    return true;
  }
  if (currentData && !newData) {
    return true;
  }
  if (!currentData && !newData) {
    return false;
  }

  return JSON.stringify(currentData) !== JSON.stringify(newData);
}

'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateStatusStep } from '../services/project.service';
import type { EStatusTask } from '../types/workflow';

export function useUpdateStatusStep() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: EStatusTask }) => updateStatusStep(id, status),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['updateStatusStep'] });
      // toast.success('Project created successfully');
    },
    onError: (error) => {
      console.error('Error creating step score:', error);
    },
  });
}

'use client';

import type { ProjectFilters } from '../services/project.service';
import { useCallback } from 'react';
import { useProjectFilterStore } from '../stores/project-filter-store/store';

/**
 * Hook for managing project filters
 *
 * This hook provides methods for managing project filters and search functionality.
 * It uses the project store for state management.
 *
 * @returns Filter-related state and methods
 */
export function useProjectFilters() {
  // Get filter-related state and actions from the store
  const {
    filters,
    searchValue,
    setFilter,
    applyFilters,
    removeFilter,
    resetFilters,
    setSearchValue,
    clearSearchValue,
  } = useProjectFilterStore();

  // Convert filters to API format
  const apiFilters: ProjectFilters = {
    status: filters.status !== 'All' ? filters.status : undefined,
    type: filters.type !== 'All' ? filters.type : undefined,
    campaign: filters.campaign !== 'All' ? filters.campaign : undefined,
    startDate: filters.startDate,
    endDate: filters.endDate,
    searchQuery: searchValue || undefined,
  };

  // Check if any filters are active
  const hasActiveFilters
    = filters.status !== 'All'
      || filters.type !== 'All'
      || filters.campaign !== 'All'
      || !!searchValue
      || !!filters.startDate
      || !!filters.endDate;

  // Function to update a filter
  const updateFilter = useCallback(<K extends keyof typeof filters>(
    key: K,
    value: typeof filters[K],
  ) => {
    // Update the filter in the store
    setFilter(key, value);
  }, [setFilter]);

  // Function to update search value
  const updateSearchValue = useCallback((value: string) => {
    // Update the search value in the store
    setSearchValue(value);
  }, [setSearchValue]);

  return {
    // State
    filters,
    searchValue,
    apiFilters,
    hasActiveFilters,

    // Actions
    setFilter,
    updateFilter,
    applyFilters,
    removeFilter,
    resetFilters,
    updateSearchValue,
    clearSearchValue,
  };
}

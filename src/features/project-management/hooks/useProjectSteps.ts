import { useQuery } from '@tanstack/react-query';
import { getProjectSteps } from '../services/project.service';

// export function useProjectSteps() {
//   const selectedProjectId = useSelectedProjectId();
//   const { setSelectedProjectId, setSelectedStepId } = useProjectDetailActions();

//   console.log('selectedProjectId', selectedProjectId);

//   const projectStepQuery = useQuery({
//     queryKey: ['projectStep', selectedProjectId],
//     queryFn: () => getProjectSteps(selectedProjectId as string),
//     select: response => response.data,
//     enabled: !!selectedProjectId,
//     staleTime: 5 * 60 * 1000, // 5 minutes - prevent constant refetching
//     refetchOnMount: false,
//     refetchOnWindowFocus: false,
//     refetchOnReconnect: false,
//   });

//   const selectProject = useCallback((id: string | null) => {
//     setSelectedProjectId(id);
//   }, [setSelectedProjectId]);

//   const selectStep = useCallback((id: string | null) => {
//     setSelectedStepId(id);
//   }, [setSelectedStepId]);

//   return useMemo(() => ({
//     projectStep: projectStepQuery.data,
//     selectProject,
//     selectStep,
//     // Loading states
//     isLoading: projectStepQuery.isLoading,
//     isFetching: projectStepQuery.isFetching,

//     // Error state
//     error: projectStepQuery.error,
//   }), [
//     projectStepQuery.data,
//     projectStepQuery.isLoading,
//     projectStepQuery.isFetching,
//     projectStepQuery.error,
//     selectProject,
//     selectStep,
//   ]);
// }

export function useProjectSteps(selectedProjectId: string) {
  return useQuery({
    queryKey: ['projectStep', selectedProjectId],
    queryFn: () => getProjectSteps(selectedProjectId),
    select: response => response.data,
    enabled: !!selectedProjectId,
    staleTime: 5 * 60 * 1000, // 5 minutes - prevent constant refetching
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}

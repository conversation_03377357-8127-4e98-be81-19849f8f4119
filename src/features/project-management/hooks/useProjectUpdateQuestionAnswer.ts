'use client';

import type { StepInfosPayload } from '../types/evaluation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { updateQuestionAnswerApi } from '../services/project.service';

/**
 * Hook for creating a new project
 *
 * This hook provides a way to create a new project.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Create mutation and helper method
 */
export function useProjectUpdateQuestionAnswer() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Create project mutation
  const updateQuestionAndAnswer = useMutation({
    mutationFn: ({ data, id }: { data: StepInfosPayload; id: string }) => updateQuestionAnswerApi(data, id),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['getInfoDetail'] });
      // toast.success('Project created successfully');
    },
    onError: (error) => {
      console.error('Error creating step score:', error);
    },
  });

  // Function to create a project
  const updateQuestionAnswer = useCallback(async (data: StepInfosPayload, id: string) => {
    return updateQuestionAndAnswer.mutateAsync({ data, id });
  }, [updateQuestionAndAnswer]);

  return {
    // Mutation state
    isCreating: updateQuestionAndAnswer.isPending,
    createError: updateQuestionAndAnswer.error,

    // Action
    updateQuestionAnswer,
  };
}
